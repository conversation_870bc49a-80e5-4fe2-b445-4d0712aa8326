# OpenAI API 配置指南

## 🔑 获取 OpenAI API 密钥

1. **访问 OpenAI 官网**
   - 前往 [https://platform.openai.com/](https://platform.openai.com/)
   - 注册或登录您的账户

2. **创建 API 密钥**
   - 点击右上角的用户头像
   - 选择 "View API keys"
   - 点击 "Create new secret key"
   - 复制生成的密钥（格式类似：`sk-...`）

## ⚙️ 配置项目

1. **设置环境变量**
   - 打开项目根目录下的 `.env` 文件
   - 将 `your_openai_api_key_here` 替换为您的实际API密钥：
   ```
   VITE_OPENAI_API_KEY=sk-your-actual-api-key-here
   ```

2. **重启开发服务器**
   ```bash
   npm run dev
   ```

## 💰 费用说明

- OpenAI API 按使用量计费
- GPT-3.5-turbo 价格约为 $0.002/1K tokens
- 生成一个绘本大约消耗 500-1000 tokens
- 建议设置使用限额以控制费用

## 🔒 安全注意事项

⚠️ **重要提醒**：
- 不要将 API 密钥提交到版本控制系统
- `.env` 文件已添加到 `.gitignore` 中
- 在生产环境中，建议通过后端API代理OpenAI请求
- 定期检查API使用情况和费用

## 🧪 测试配置

配置完成后，您可以：
1. 启动应用程序
2. 完成角色设置和故事设置
3. 在内容设置页面点击"立即生成绘本"
4. 观察是否成功调用OpenAI API生成内容

## 🔧 故障排除

**如果遇到问题**：
1. 检查API密钥是否正确设置
2. 确认OpenAI账户有足够余额
3. 检查网络连接
4. 查看浏览器控制台的错误信息

**常见错误**：
- `401 Unauthorized`: API密钥无效或未设置
- `429 Too Many Requests`: 请求频率过高，稍后重试
- `insufficient_quota`: 账户余额不足

## 📞 获取帮助

如果仍有问题，请：
- 查看 [OpenAI API 文档](https://platform.openai.com/docs)
- 检查 [OpenAI 状态页面](https://status.openai.com/)
- 联系 OpenAI 支持团队
