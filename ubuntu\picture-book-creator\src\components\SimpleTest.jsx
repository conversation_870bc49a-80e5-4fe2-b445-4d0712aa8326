import { useState } from 'react'

// 直接导入 Radix UI 组件来测试
import * as SliderPrimitive from "@radix-ui/react-slider"
import * as SwitchPrimitive from "@radix-ui/react-switch"

export default function SimpleTest() {
  const [sliderValue, setSliderValue] = useState([6])
  const [switchValue, setSwitchValue] = useState(false)

  return (
    <div className="min-h-screen bg-white p-8">
      <div className="max-w-2xl mx-auto space-y-8">
        <h1 className="text-2xl font-bold">简单组件测试</h1>
        
        {/* 直接使用 Radix Slider */}
        <div className="space-y-4 border p-4 rounded">
          <h2 className="text-lg font-semibold">Radix Slider 测试</h2>
          <p>当前值: {sliderValue[0]}</p>
          <SliderPrimitive.Root
            value={sliderValue}
            onValueChange={setSliderValue}
            max={10}
            min={4}
            step={1}
            className="relative flex w-full touch-none items-center select-none h-5"
          >
            <SliderPrimitive.Track className="bg-gray-200 relative grow overflow-hidden rounded-full h-2 w-full">
              <SliderPrimitive.Range className="bg-blue-500 absolute h-full" />
            </SliderPrimitive.Track>
            <SliderPrimitive.Thumb className="block w-5 h-5 bg-white border-2 border-blue-500 rounded-full shadow-lg" />
          </SliderPrimitive.Root>
        </div>

        {/* 直接使用 Radix Switch */}
        <div className="space-y-4 border p-4 rounded">
          <h2 className="text-lg font-semibold">Radix Switch 测试</h2>
          <div className="flex items-center space-x-4">
            <span>当前状态: {switchValue ? '开启' : '关闭'}</span>
            <SwitchPrimitive.Root
              checked={switchValue}
              onCheckedChange={setSwitchValue}
              className="w-11 h-6 bg-gray-200 rounded-full relative data-[state=checked]:bg-blue-500 transition-colors"
            >
              <SwitchPrimitive.Thumb className="block w-5 h-5 bg-white rounded-full shadow-lg transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0" />
            </SwitchPrimitive.Root>
          </div>
        </div>

        {/* 原生HTML对比 */}
        <div className="space-y-4 border p-4 rounded">
          <h2 className="text-lg font-semibold">原生HTML对比</h2>
          <div className="space-y-2">
            <input 
              type="range" 
              min="4" 
              max="10" 
              value={sliderValue[0]} 
              onChange={(e) => setSliderValue([parseInt(e.target.value)])} 
              className="w-full" 
            />
            <p>原生滑块值: {sliderValue[0]}</p>
            <label className="flex items-center space-x-2">
              <input 
                type="checkbox" 
                checked={switchValue} 
                onChange={(e) => setSwitchValue(e.target.checked)} 
              />
              <span>原生开关: {switchValue ? '开启' : '关闭'}</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  )
}
