import { useState } from 'react'
import { Button } from '@/components/ui/button.jsx'
import { Input } from '@/components/ui/input.jsx'
import { Label } from '@/components/ui/label.jsx'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group.jsx'
import { ArrowLeft, ArrowRight, User } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

export default function CharacterSetupPage() {
  const navigate = useNavigate()
  const [characterData, setCharacterData] = useState({
    name: '',
    age: 6,
    identity: 'human',
    gender: 'any'
  })

  const handleNext = () => {
    // 如果姓名为空，生成随机姓名
    if (!characterData.name.trim()) {
      const randomNames = ['小明', '小红', '小华', '小丽', '小强', '小美', '小杰', '小雨']
      const randomName = randomNames[Math.floor(Math.random() * randomNames.length)]
      setCharacterData(prev => ({ ...prev, name: randomName }))
    }
    
    // 保存数据到localStorage
    localStorage.setItem('characterData', JSON.stringify(characterData))
    navigate('/story-setup')
  }

  const handleBack = () => {
    navigate('/')
  }

  return (
    <div className="min-h-screen bg-white">
      {/* 顶部导航栏 */}
      <div className="bg-white border-b border-gray-100 px-6 py-4">
        <div className="max-w-2xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <User className="w-6 h-6 text-blue-500 mr-3" />
            <h1 className="text-xl font-medium text-gray-800">角色设定</h1>
          </div>
          <div className="text-sm text-gray-500">步骤 1/3</div>
        </div>
        
        {/* 进度条 */}
        <div className="max-w-2xl mx-auto mt-4">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-blue-500 h-2 rounded-full w-1/3 transition-all duration-300"></div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-2xl mx-auto px-6 py-12">
        <div className="space-y-8">
          {/* 角色姓名 */}
          <div className="space-y-3">
            <Label htmlFor="name" className="text-base font-medium text-gray-700">
              角色姓名
            </Label>
            <Input
              id="name"
              type="text"
              placeholder="请输入角色姓名（可选，留空将随机生成）"
              value={characterData.name}
              onChange={(e) => setCharacterData(prev => ({ ...prev, name: e.target.value }))}
              className="text-base py-3 rounded-xl border-gray-200 focus:border-blue-500"
            />
          </div>

          {/* 角色年龄 */}
          <div className="space-y-3">
            <Label htmlFor="age" className="text-base font-medium text-gray-700">
              角色年龄
            </Label>
            <Input
              id="age"
              type="number"
              min="3"
              max="12"
              value={characterData.age}
              onChange={(e) => setCharacterData(prev => ({ ...prev, age: parseInt(e.target.value) }))}
              className="text-base py-3 rounded-xl border-gray-200 focus:border-blue-500"
            />
            <p className="text-sm text-gray-500">适合年龄：3-12岁</p>
          </div>

          {/* 角色身份 */}
          <div className="space-y-4">
            <Label className="text-base font-medium text-gray-700">角色身份</Label>
            <RadioGroup
              value={characterData.identity}
              onValueChange={(value) => setCharacterData(prev => ({ ...prev, identity: value }))}
              className="flex space-x-8"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="human" id="human" />
                <Label htmlFor="human" className="text-base cursor-pointer">人类</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="animal" id="animal" />
                <Label htmlFor="animal" className="text-base cursor-pointer">动物</Label>
              </div>
            </RadioGroup>
          </div>

          {/* 角色性别 */}
          <div className="space-y-4">
            <Label className="text-base font-medium text-gray-700">角色性别</Label>
            <RadioGroup
              value={characterData.gender}
              onValueChange={(value) => setCharacterData(prev => ({ ...prev, gender: value }))}
              className="flex space-x-8"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="boy" id="boy" />
                <Label htmlFor="boy" className="text-base cursor-pointer">男孩</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="girl" id="girl" />
                <Label htmlFor="girl" className="text-base cursor-pointer">女孩</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="any" id="any" />
                <Label htmlFor="any" className="text-base cursor-pointer">不限</Label>
              </div>
            </RadioGroup>
          </div>
        </div>
      </div>

      {/* 底部按钮 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 px-6 py-4">
        <div className="max-w-2xl mx-auto flex justify-between">
          <Button
            onClick={handleBack}
            variant="outline"
            className="px-6 py-3 rounded-xl border-gray-200 hover:bg-gray-50"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回首页
          </Button>
          <Button
            onClick={handleNext}
            className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-xl"
          >
            下一步
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  )
}

