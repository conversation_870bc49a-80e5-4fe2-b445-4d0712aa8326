import { useState } from 'react'
import { Button } from '@/components/ui/button.jsx'

export default function ImageTestPage() {
  const [testImageUrl, setTestImageUrl] = useState('')
  const [imageStatus, setImageStatus] = useState('')

  // 测试用的示例图像URL
  const testUrls = [
    'https://picsum.photos/400/300?random=1',
    'https://picsum.photos/400/300?random=2',
    'https://via.placeholder.com/400x300/0066cc/ffffff?text=Test+Image'
  ]

  const testImage = (url) => {
    setTestImageUrl(url)
    setImageStatus('加载中...')
  }

  const handleImageLoad = () => {
    setImageStatus('✅ 图像加载成功')
  }

  const handleImageError = () => {
    setImageStatus('❌ 图像加载失败')
  }

  return (
    <div className="min-h-screen bg-white p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold mb-8">图像显示测试页面</h1>
        
        <div className="space-y-6">
          {/* 测试按钮 */}
          <div className="space-y-2">
            <h2 className="text-lg font-semibold">测试图像URL</h2>
            {testUrls.map((url, index) => (
              <Button 
                key={index}
                onClick={() => testImage(url)}
                variant="outline"
                className="mr-2 mb-2"
              >
                测试图像 {index + 1}
              </Button>
            ))}
          </div>

          {/* 自定义URL测试 */}
          <div className="space-y-2">
            <h2 className="text-lg font-semibold">自定义URL测试</h2>
            <div className="flex space-x-2">
              <input
                type="url"
                placeholder="输入图像URL"
                className="flex-1 px-3 py-2 border rounded"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    testImage(e.target.value)
                  }
                }}
              />
              <Button onClick={() => {
                const input = document.querySelector('input[type="url"]')
                testImage(input.value)
              }}>
                测试
              </Button>
            </div>
          </div>

          {/* 状态显示 */}
          {imageStatus && (
            <div className="p-3 bg-gray-100 rounded">
              <strong>状态:</strong> {imageStatus}
            </div>
          )}

          {/* 图像显示区域 */}
          {testImageUrl && (
            <div className="border-2 border-dashed border-gray-300 p-4 rounded-lg">
              <h3 className="text-md font-medium mb-4">图像显示测试</h3>
              <div className="text-center">
                <img
                  src={testImageUrl}
                  alt="测试图像"
                  className="max-w-full max-h-96 mx-auto rounded-lg shadow-lg"
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                />
              </div>
              <div className="mt-4 text-xs text-gray-600 break-all">
                <strong>URL:</strong> {testImageUrl}
              </div>
            </div>
          )}

          {/* 模拟绘本页面结构 */}
          <div className="border-2 border-blue-300 p-4 rounded-lg">
            <h3 className="text-md font-medium mb-4">模拟绘本页面结构</h3>
            <div className="space-y-4">
              {/* 模拟AI生成的图像 */}
              <div className="mb-6">
                <div className="relative">
                  {testImageUrl ? (
                    <img 
                      src={testImageUrl}
                      alt="模拟AI生成的插画"
                      className="w-full max-w-md mx-auto rounded-2xl shadow-lg block"
                      onLoad={() => console.log('模拟绘本图像加载成功')}
                      onError={() => console.error('模拟绘本图像加载失败')}
                    />
                  ) : (
                    <div className="text-6xl text-center">🌟</div>
                  )}
                </div>
                <div className="text-center mt-2 text-xs text-gray-500">
                  {testImageUrl ? 'AI生成的插画' : '默认插图'}
                </div>
              </div>

              {/* 模拟故事内容 */}
              <h2 className="text-2xl font-semibold text-gray-800">
                测试故事标题
              </h2>
              <p className="text-lg text-gray-700 leading-relaxed">
                这是一个测试故事的内容。我们正在测试图像是否能够正确显示在绘本页面中。
              </p>
            </div>
          </div>

          {/* 浏览器信息 */}
          <div className="bg-gray-50 p-4 rounded text-sm">
            <h3 className="font-medium mb-2">浏览器信息</h3>
            <div>User Agent: {navigator.userAgent}</div>
            <div>支持的图像格式: JPEG, PNG, GIF, WebP, SVG</div>
          </div>
        </div>
      </div>
    </div>
  )
}
