import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button.jsx'
import { ChevronLeft, ChevronRight, Home, RotateCcw, BookOpen } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

export default function PreviewPage() {
  const navigate = useNavigate()
  const [currentPage, setCurrentPage] = useState(0)
  const [bookData, setBookData] = useState(null)

  useEffect(() => {
    // 获取所有设置数据
    const characterData = JSON.parse(localStorage.getItem('characterData') || '{}')
    const storyData = JSON.parse(localStorage.getItem('storyData') || '{}')
    const contentData = JSON.parse(localStorage.getItem('contentData') || '{}')

    if (!characterData.name) {
      navigate('/')
      return
    }

    // 生成示例绘本内容
    const storyTypes = {
      'adventure': '冒险故事',
      'growth': '成长故事', 
      'friendship': '友情故事',
      'life-skills': '生活技能'
    }

    const pages = []
    const totalPages = storyData.pages || 6
    
    // 封面
    pages.push({
      type: 'cover',
      title: `${characterData.name}的${storyTypes[storyData.type] || '奇妙'}之旅`,
      subtitle: contentData.isCustom ? contentData.customContent : contentData.randomTopic,
      image: '🌟'
    })

    // 内容页
    for (let i = 1; i < totalPages; i++) {
      pages.push({
        type: 'content',
        pageNumber: i,
        title: `第${i}页`,
        content: `这是${characterData.name}的故事第${i}页。在这一页中，${characterData.name}遇到了新的挑战和机会，学习了重要的人生道理...`,
        image: ['🌈', '🦋', '🌸', '🌺', '🍀', '⭐'][i % 6]
      })
    }

    // 结尾页
    pages.push({
      type: 'ending',
      title: '故事结束',
      content: `${characterData.name}通过这次经历学会了很多，变得更加勇敢和智慧。`,
      image: '🎉'
    })

    setBookData({
      character: characterData,
      story: storyData,
      content: contentData,
      pages: pages
    })
  }, [navigate])

  const handlePrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1)
    }
  }

  const handleNextPage = () => {
    if (bookData && currentPage < bookData.pages.length - 1) {
      setCurrentPage(currentPage + 1)
    }
  }

  const handleBackHome = () => {
    navigate('/')
  }

  const handleRecreate = () => {
    localStorage.clear()
    navigate('/')
  }

  if (!bookData) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <BookOpen className="w-16 h-16 text-blue-500 mx-auto mb-4" />
          <p className="text-gray-500">加载中...</p>
        </div>
      </div>
    )
  }

  const currentPageData = bookData.pages[currentPage]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      {/* 顶部导航 */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200 px-6 py-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <BookOpen className="w-6 h-6 text-blue-500 mr-3" />
            <h1 className="text-xl font-medium text-gray-800">绘本预览</h1>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-500">
              {currentPage + 1} / {bookData.pages.length}
            </span>
            <Button onClick={handleBackHome} variant="outline" size="sm">
              <Home className="w-4 h-4 mr-2" />
              返回首页
            </Button>
            <Button onClick={handleRecreate} variant="outline" size="sm">
              <RotateCcw className="w-4 h-4 mr-2" />
              重新创建
            </Button>
          </div>
        </div>
      </div>

      {/* 绘本内容 */}
      <div className="max-w-4xl mx-auto px-6 py-12">
        <div className="bg-white rounded-3xl shadow-2xl overflow-hidden">
          {/* 页面内容 */}
          <div className="aspect-[4/3] p-12 flex flex-col items-center justify-center text-center">
            {currentPageData.type === 'cover' && (
              <div className="space-y-6">
                <div className="text-8xl mb-6">{currentPageData.image}</div>
                <h1 className="text-4xl font-bold text-gray-800 mb-4">
                  {currentPageData.title}
                </h1>
                <p className="text-xl text-gray-600 max-w-md">
                  {currentPageData.subtitle}
                </p>
              </div>
            )}

            {currentPageData.type === 'content' && (
              <div className="space-y-8 max-w-2xl">
                <div className="text-6xl mb-6">{currentPageData.image}</div>
                <h2 className="text-2xl font-semibold text-gray-800">
                  {currentPageData.title}
                </h2>
                <p className="text-lg text-gray-700 leading-relaxed">
                  {currentPageData.content}
                </p>
              </div>
            )}

            {currentPageData.type === 'ending' && (
              <div className="space-y-6">
                <div className="text-8xl mb-6">{currentPageData.image}</div>
                <h2 className="text-3xl font-bold text-gray-800 mb-4">
                  {currentPageData.title}
                </h2>
                <p className="text-xl text-gray-600 max-w-md">
                  {currentPageData.content}
                </p>
              </div>
            )}
          </div>

          {/* 翻页控制 */}
          <div className="bg-gray-50 px-8 py-6 flex items-center justify-between">
            <Button
              onClick={handlePrevPage}
              disabled={currentPage === 0}
              variant="outline"
              className="flex items-center"
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              上一页
            </Button>

            <div className="flex space-x-2">
              {bookData.pages.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentPage(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentPage ? 'bg-blue-500' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>

            <Button
              onClick={handleNextPage}
              disabled={currentPage === bookData.pages.length - 1}
              variant="outline"
              className="flex items-center"
            >
              下一页
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>

        {/* 绘本信息 */}
        <div className="mt-8 bg-white rounded-2xl p-6 shadow-lg">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">绘本信息</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-500">主角姓名：</span>
              <span className="font-medium">{bookData.character.name}</span>
            </div>
            <div>
              <span className="text-gray-500">主角年龄：</span>
              <span className="font-medium">{bookData.character.age}岁</span>
            </div>
            <div>
              <span className="text-gray-500">角色身份：</span>
              <span className="font-medium">{bookData.character.identity === 'human' ? '人类' : '动物'}</span>
            </div>
            <div>
              <span className="text-gray-500">故事页数：</span>
              <span className="font-medium">{bookData.pages.length}页</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

