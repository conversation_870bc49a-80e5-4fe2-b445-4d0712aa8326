import { useState, useEffect } from 'react'

export default function SimpleDebugPage() {
  const [data, setData] = useState('加载中...')

  useEffect(() => {
    try {
      const characterData = localStorage.getItem('characterData')
      const storyData = localStorage.getItem('storyData')
      const contentData = localStorage.getItem('contentData')
      const generatedBook = localStorage.getItem('generatedBook')

      setData({
        characterData: characterData || '无',
        storyData: storyData || '无',
        contentData: contentData || '无',
        generatedBook: generatedBook || '无'
      })
    } catch (error) {
      setData('错误: ' + error.message)
    }
  }, [])

  const clearStorage = () => {
    localStorage.clear()
    window.location.reload()
  }

  const createTestData = () => {
    const testData = {
      title: '测试绘本',
      pages: [
        {
          pageNumber: 1,
          title: '第一页',
          content: '这是测试内容',
          imageUrl: null
        }
      ]
    }
    
    localStorage.setItem('characterData', JSON.stringify({ name: '测试角色', age: 6 }))
    localStorage.setItem('generatedBook', JSON.stringify(testData))
    alert('测试数据已创建')
    window.location.reload()
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>简单调试页面</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <button onClick={createTestData} style={{ marginRight: '10px', padding: '10px' }}>
          创建测试数据
        </button>
        <button onClick={clearStorage} style={{ padding: '10px', backgroundColor: 'red', color: 'white' }}>
          清空数据
        </button>
      </div>

      <div style={{ backgroundColor: '#f5f5f5', padding: '20px', borderRadius: '5px' }}>
        <h2>LocalStorage 数据:</h2>
        <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
          {typeof data === 'string' ? data : JSON.stringify(data, null, 2)}
        </pre>
      </div>

      <div style={{ marginTop: '20px' }}>
        <a href="/preview" style={{ color: 'blue', textDecoration: 'underline' }}>
          访问预览页面
        </a>
      </div>
    </div>
  )
}
